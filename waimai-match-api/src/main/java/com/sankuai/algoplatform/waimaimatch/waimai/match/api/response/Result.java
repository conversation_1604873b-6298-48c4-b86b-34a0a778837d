package com.sankuai.algoplatform.waimaimatch.waimai.match.api.response;

import java.io.Serializable;
import java.util.List;

/**
 * 匹配结果类
 */
public class Result implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 匹配中间表主键，用于关联表数据
     */
    private long reqId;

    private Boolean success;

    private String message;

    /**
     * 匹配数据列表
     */
    private List<MatchRecord> matchRecords;

    // 构造函数

    /**
     * 默认构造函数
     */
    public Result() {
    }

    /**
     * 带参数的构造函数
     *
     * @param reqId 匹配中间表主键
     * @param matchRecords 匹配数据列表
     */
    public Result(long reqId, List<MatchRecord> matchRecords) {
        this.reqId = reqId;
        this.matchRecords = matchRecords;
    }

    // Getter and Setter methods

    public long getReqId() {
        return reqId;
    }

    public void setReqId(long reqId) {
        this.reqId = reqId;
    }

    public List<MatchRecord> getMatchRecords() {
        return matchRecords;
    }

    public void setMatchRecords(List<MatchRecord> matchRecords) {
        this.matchRecords = matchRecords;
    }

    // 便利方法

    /**
     * 获取匹配记录数量
     *
     * @return 匹配记录数量
     */
    public int getMatchRecordCount() {
        return matchRecords != null ? matchRecords.size() : 0;
    }

    /**
     * 判断是否有匹配记录
     *
     * @return true if has match records, false otherwise
     */
    public boolean hasMatchRecords() {
        return matchRecords != null && !matchRecords.isEmpty();
    }

    /**
     * 获取成功匹配的记录数量
     *
     * @return 成功匹配的记录数量
     */
    public long getSuccessMatchCount() {
        if (matchRecords == null) {
            return 0;
        }
        return matchRecords.stream()
                .filter(MatchRecord::isMatched)
                .count();
    }

    /**
     * 获取历史匹配的记录数量
     *
     * @return 历史匹配的记录数量
     */
    public long getHistoryMatchCount() {
        if (matchRecords == null) {
            return 0;
        }
        return matchRecords.stream()
                .filter(MatchRecord::isHistoryMatch)
                .count();
    }

    /**
     * 获取即时匹配的记录数量
     *
     * @return 即时匹配的记录数量
     */
    public long getRealtimeMatchCount() {
        if (matchRecords == null) {
            return 0;
        }
        return matchRecords.stream()
                .filter(MatchRecord::isRealtimeMatch)
                .count();
    }

    /**
     * 获取生成新ID的记录数量
     *
     * @return 生成新ID的记录数量
     */
    public long getNewIdCount() {
        if (matchRecords == null) {
            return 0;
        }
        return matchRecords.stream()
                .filter(MatchRecord::hasNewId)
                .count();
    }
}
